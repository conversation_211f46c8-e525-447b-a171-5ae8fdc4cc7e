import { ChangeDetectorR<PERSON>, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GridApi, GridOptions } from 'ag-grid-community';

import { AppState } from 'src/app/app.reducer';
import { DateRange } from 'src/app/app.enum';
import { getTenantName, getDateRange, setTimeZoneDate, validateAllFormFields } from 'src/app/core/utils/common.util';
import { NotificationsService } from 'angular2-notifications';
import { IntegrationService } from 'src/app/services/controllers/integration.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

import {
  getGoogleCampaignAccounts,
  getGoogleCampaignAccountsIsLoading,
  getGoogleAdsAccountAds,
  getGoogleAdsAccountAdsIsLoading,
  getGoogleLeadsMarketingFinances,
  getGoogleLeadsMarketingFinancesIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchGoogleCampaignAccounts,
  FetchGoogleAdsAccountAds,
  FetchGoogleLeadsMarketingFinances,
  DeleteGoogleCampaignAccount,
} from 'src/app/reducers/Integration/integration.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
@Component({
  selector: 'app-google-campaign',
  templateUrl: './google-campaign.component.html'
})
export class GoogleCampaignComponent implements OnInit, OnDestroy {
  private stopper = new Subject<void>();

  accounts: any[] = [];
  filteredAccounts: any[] = [];
  allAccounts: any[] = [];
  isAccountsLoading = false;

  // Data for ads and campaigns
  adsData: any[] = [];
  isAdsLoading = false;
  campaignsData: any[] = [];
  isCampaignsLoading = false;
  campaignsMarketingData: any[] = [];
  isCampaignsMarketingLoading = false;

  // Pagination and filters for accounts API
  accountsPageNumber = 1;
  accountsPageSize = 10;
  leadSource = 44;

  userBasicDetails: any;
  currentDate = new Date();
  modalRef: BsModalRef;
  showLeftNav = false;

  // AG Grid
  gridApi: GridApi;
  gridOptions: GridOptions;
  gridOptionsCampaigns: GridOptions;

  // Pagination
  adsPageNumber = 1;
  adsPageSize = 10;
  adsTotalCount = 0;
  adsShowEntriesSize = [10, 25, 50, 100];

  campaignsPageNumber = 1;
  campaignsPageSize = 10;
  campaignsTotalCount = 0;
  showEntriesSize = [10, 25, 50, 100];
  campaignsShowEntriesSize = [10, 25, 50, 100];

  // Search
  searchTerm = '';
  campaignsSearchTerm = '';

  // Date Filter
  cplTrackForm: FormGroup;
  showCplFilter = false;
  campaignsCplTrackForm: FormGroup;
  showCampaignsCplFilter = false;
  cplDateFilterList = [
    { value: 'Today', displayName: 'Today' },
    { value: 'Yesterday', displayName: 'Yesterday' },
    { value: 'Last7Days', displayName: 'Last 7 Days' },
    { value: 'Last30Days', displayName: 'Last 30 Days' },
    { value: 'Custom', displayName: 'Custom' }
  ];

  // Permissions
  canAdd = true;
  canDelete = true;
  canView = true;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private modalService: BsModalService,
    private _notificationService: NotificationsService,
    private integrationService: IntegrationService,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private headerTitle: HeaderTitleService,
    private commonService: CommonService,
    private gridOptionsService: GridOptionsService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setTitle('Google Campaign Integration');
    this.initializeComponent();
    this.initializeGridOptions();
    this.initializeDateFilter();
    this.loadAccounts();
    this.subscribeToStore();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }

  private initializeComponent(): void {
    this.store.select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((settings: any) => {
        this.userBasicDetails = settings;
      });
  }

  private subscribeToStore(): void {
    this.store.select(getGoogleCampaignAccounts)
      .pipe(takeUntil(this.stopper))
      .subscribe((accounts: any[]) => {
        if (accounts && accounts.length >= 0) {
          // Transform API response to match component structure
          this.accounts = accounts.map(account => ({
            id: account.id,
            accountName: account.accountName,
            customerId: account.customerId,
            leadCount: account.leadCount || 0,
            status: account.status,
            isAdsExpanded: false,
            isAdsLoading: false,
            isCampaignsExpanded: false,
            isCampaignsLoading: false,
            adsPageNumber: 1,
            adsPageSize: 10,
            adsTotalCount: 0,
            campaignsPageNumber: 1,
            campaignsPageSize: 10,
            campaignsTotalCount: 0,
            ads: [],
            paginatedAds: [],
            campaigns: [],
            paginatedCampaigns: [],
            adsLoaded: false,
            campaignsLoaded: false
          }));
          this.filteredAccounts = [...this.accounts];
          console.log(this.filteredAccounts,'filteredAccounts');
          this.allAccounts = [...this.accounts];
        }
      });

    this.store.select(getGoogleCampaignAccountsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAccountsLoading = isLoading;
      });
  }

  initializeGridOptions(): void {
    // Grid options for Google Ads (account-ads API)
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Ad Name',
        field: 'Ad Name',
        minWidth: 150,
        valueGetter: (params) => params.data?.adName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Ad Set Name',
        field: 'Ad Set Name',
        minWidth: 120,
        valueGetter: (params) => params.data?.adSetName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Ad Set ID',
        field: 'Ad Set ID',
        minWidth: 100,
        valueGetter: (params) => params.data?.adSetId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params) => params.data?.leadsCount,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || 0}</p>`;
        },
      },
      {
        headerName: 'Country',
        field: 'Country',
        minWidth: 100,
        valueGetter: (params) => params.data?.countryCode,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Currency',
        field: 'Currency',
        minWidth: 100,
        valueGetter: (params) => params.data?.currencyCode,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 120,
        valueGetter: (params) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status || '--'}</p>`;
        },
      }
    ];

    this.gridOptions.context = {
      componentParent: this,
      componentType: 'ads',
    };

    // Grid options for Google Campaigns (marketing finances API)
    this.gridOptionsCampaigns = this.gridOptionsService.getGridSettings(this);
    this.gridOptionsCampaigns.rowHeight = 60;
    this.gridOptionsCampaigns.columnDefs = [
      {
        headerName: 'Campaign Name',
        field: 'Campaign Name',
        minWidth: 150,
        valueGetter: (params) => params.data?.campaignName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Google Ad Leads',
        field: 'Google Ad Leads',
        minWidth: 120,
        valueGetter: (params) => params.data?.googleAdLeads,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'CPL Per User',
        field: 'CPL Per User',
        minWidth: 120,
        valueGetter: (params) => {
          const cpl = params.data?.cplPerUser;
          return cpl !== null && cpl !== undefined && cpl !== 0 ? '$' + cpl.toFixed(2) : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Meetings Done',
        field: 'Meetings Done',
        minWidth: 120,
        valueGetter: (params) => params.data?.noOfMeetingDone,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Site Visits Done',
        field: 'Site Visits Done',
        minWidth: 120,
        valueGetter: (params) => params.data?.noOfSiteVisitDone,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Invoiced',
        field: 'Invoiced',
        minWidth: 100,
        valueGetter: (params) => params.data?.noOfInvoiced,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Total Revenue',
        field: 'Total Revenue',
        minWidth: 120,
        valueGetter: (params) => {
          const revenue = params.data?.totalRevenue;
          return revenue !== null && revenue !== undefined && revenue !== 0 ? '$' + revenue.toFixed(2) : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'ROI',
        field: 'ROI',
        minWidth: 100,
        valueGetter: (params) => {
          const roi = params.data?.roi;
          return roi !== null && roi !== undefined && roi !== 0 ? roi.toFixed(2) + '%' : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      }
    ];

    this.gridOptionsCampaigns.context = {
      componentParent: this,
      componentType: 'campaigns',
    };
  }

  initializeDateFilter(): void {
    this.cplTrackForm = this.formBuilder.group({
      cplTrackRange: ['Today'],
      cplTrackDate: [null]
    });

    this.campaignsCplTrackForm = this.formBuilder.group({
      cplTrackRange: ['Today'],
      cplTrackDate: [null]
    });
  }

  login() {
    if (this.canAdd) {
      const idToken = localStorage?.getItem('idToken');
      const tenant = getTenantName();
      window.location.href = `https://integration.leadrat.info/google-ads/?id=${idToken}&tenant=${tenant}&returnUrl=${encodeURIComponent(window.location.href)}`;
    } else {
      this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
    }
    return;
  }

  loadAccounts(): void {
    const payload = {
      LeadSource: this.leadSource,
      PageNumber: this.accountsPageNumber,
      PageSize: this.accountsPageSize
    };
    this.store.dispatch(new FetchGoogleCampaignAccounts(payload));
  }

  deleteAccount(accountId: string, accountName: string): void {
    this.store.dispatch(new DeleteGoogleCampaignAccount(accountId));
  }



  syncAds(accountId: string): void {
    this._notificationService.success('Ads sync initiated');
    // Uncomment when API is ready
    // this.store.dispatch(new SyncGoogleCampaignAds(accountId));
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filteredAccounts = [...this.allAccounts];
  }

  toggleExpand(account: any, expandType: string): void {
    // Store current state before closing others
    const currentState = account[expandType];

    // Close all other expanded sections
    this.filteredAccounts.forEach(acc => {
      acc.isAdsExpanded = false;
      acc.isCampaignsExpanded = false;
    });

    if (expandType === 'isAdsExpanded') {
      account.isAdsExpanded = !currentState;
      if (account.isAdsExpanded && !account.adsLoaded) {
        account.isAdsLoading = true;
        this.loadAdsData(account);
      }
    } else if (expandType === 'isCampaignsExpanded') {
      account.isCampaignsExpanded = !currentState;
      if (account.isCampaignsExpanded && !account.campaignsLoaded) {
        account.isCampaignsLoading = true;
        this.loadCampaignsData(account);
      }
    }
  }

  loadAdsData(account: any): void {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const selectedDateRange = this.cplTrackForm.value.cplTrackRange !== 'Custom'
      ? DateRange[this.cplTrackForm.value.cplTrackRange as keyof typeof DateRange] || DateRange.Today
      : null;

    const dateRangeValues = selectedDateRange !== null
      ? getDateRange(selectedDateRange as DateRange, this.currentDate)
      : null;

    const searchText = this.searchTerm && this.searchTerm.trim() ? this.searchTerm.trim() : undefined;

    const payload: any = {
      path: 'integration/google-ads/account-ads',
      accountId: account.id,
      PageNumber: account.adsPageNumber,
      PageSize: account.adsPageSize,
      TimeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId,
      BaseUTcOffset: timeZoneOffset,
      FromDate: this.cplTrackForm.value.cplTrackDate && this.cplTrackForm.value.cplTrackRange === 'Custom'
        ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate[0], timeZoneOffset)
        : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
      ToDate: this.cplTrackForm.value.cplTrackDate && this.cplTrackForm.value.cplTrackRange === 'Custom'
        ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate[1], timeZoneOffset)
        : setTimeZoneDate(dateRangeValues?.[1] || this.currentDate, timeZoneOffset)
    };

    if (searchText) {
      payload.SearchText = searchText;
    }

    this.commonService.getModuleListByAdvFilter(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          console.log('Ads API Response:', response);

          // Always set loading to false and mark as loaded
          account.isAdsLoading = false;
          account.adsLoaded = true;

          if (response && response.items && response.items.length > 0) {
            console.log('Ads data found:', response.items.length, 'items');
            account.ads = response.items;
            account.paginatedAds = response.items;
            account.adsTotalCount = response.totalCount || response.items.length;
          } else {
            console.log('No ads data found, setting empty arrays');
            // No data found - set empty arrays
            account.ads = [];
            account.paginatedAds = [];
            account.adsTotalCount = 0;
          }
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading ads data:', error);
          account.isAdsLoading = false;
          account.adsLoaded = true;
          account.ads = [];
          account.paginatedAds = [];
          account.adsTotalCount = 0;
          this.cdr.detectChanges();
        }
      });
  }

  loadCampaignsData(account: any): void {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const selectedDateRange = this.campaignsCplTrackForm.value.cplTrackRange !== 'Custom'
      ? DateRange[this.campaignsCplTrackForm.value.cplTrackRange as keyof typeof DateRange] || DateRange.Today
      : null;

    const dateRangeValues = selectedDateRange !== null
      ? getDateRange(selectedDateRange as DateRange, this.currentDate)
      : null;

    const searchText = this.campaignsSearchTerm && this.campaignsSearchTerm.trim() ? this.campaignsSearchTerm.trim() : undefined;

    // First API Call: Google Ads Account Campaigns
    const campaignsPayload: any = {
      path: 'integration/googleads/account-campaigns',
      accountId: account.id,
      PageNumber: account.campaignsPageNumber,
      PageSize: account.campaignsPageSize,
      TimeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId,
      BaseUTcOffset: timeZoneOffset,
      FromDate: this.campaignsCplTrackForm.value.cplTrackDate && this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
        ? setTimeZoneDate(this.campaignsCplTrackForm.value.cplTrackDate[0], timeZoneOffset)
        : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
      ToDate: this.campaignsCplTrackForm.value.cplTrackDate && this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
        ? setTimeZoneDate(this.campaignsCplTrackForm.value.cplTrackDate[1], timeZoneOffset)
        : setTimeZoneDate(dateRangeValues?.[1] || this.currentDate, timeZoneOffset)
    };
    if (searchText) {
      campaignsPayload.SearchText = searchText;
    }

    console.log('Loading campaigns data for account:', account.id);
    console.log('Campaigns API payload:', campaignsPayload);

    // Call first API to get campaigns
    this.commonService.getModuleListByAdvFilter(campaignsPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          console.log('Account Campaigns API Response:', response);
          account.isCampaignsLoading = false;
          account.campaignsLoaded = true;

          if (response && response.items && response.items.length > 0) {
            account.campaigns = response.items;
            account.paginatedCampaigns = response.items;
            account.campaignsTotalCount = response.totalCount || response.items.length;

            // After getting campaigns, call marketing finances API with campaign IDs
            console.log('Campaigns loaded successfully, now calling marketing finances API');
            this.fetchCampaignsMarketingData(account, response.items);
          } else {
            account.campaigns = [];
            account.paginatedCampaigns = [];
            account.campaignsTotalCount = 0;
            console.log('No campaigns found');
          }
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading account campaigns data:', error);
          account.isCampaignsLoading = false;
          account.campaignsLoaded = true;
          account.campaigns = [];
          account.paginatedCampaigns = [];
          account.campaignsTotalCount = 0;
          this.cdr.detectChanges();
        }
      });
  }

  fetchCampaignsMarketingData(account: any, campaigns: any[]): void {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const accountId = account?.id;

    if (!accountId || !campaigns?.length) {
      console.log('fetchCampaignsMarketingData: No accountId or campaigns data');
      return;
    }

    try {
      // Prepare campaign IDs array like Facebook pattern
      const campaignIds = campaigns
        .map((campaign: any) => ({
          campaignId: campaign?.campaignId || campaign?.id,
          AdAccountId: accountId
        }))
        .filter(({ campaignId }) => !!campaignId);

      if (!campaignIds.length) {
        console.log('fetchCampaignsMarketingData: No valid campaign IDs found');
        return;
      }

      const selectedDateRange = this.campaignsCplTrackForm.value.cplTrackRange !== 'Custom'
        ? DateRange[this.campaignsCplTrackForm.value.cplTrackRange as keyof typeof DateRange] || DateRange.Today
        : null;

      const dateRangeValues = selectedDateRange !== null
        ? getDateRange(selectedDateRange as DateRange, this.currentDate)
        : null;

      // Marketing Finances API payload
      const marketingPayload: any = {
        path: 'dashboard/user/googleads/marketingfinances/campaigns',
        AccountId: accountId,
        CampaignIds: campaignIds,
        TimeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId,
        BaseUTcOffset: timeZoneOffset,
        FromDate: this.campaignsCplTrackForm.value.cplTrackDate && this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(this.campaignsCplTrackForm.value.cplTrackDate[0], timeZoneOffset)
          : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
        ToDate: this.campaignsCplTrackForm.value.cplTrackDate && this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(this.campaignsCplTrackForm.value.cplTrackDate[1], timeZoneOffset)
          : setTimeZoneDate(dateRangeValues?.[1] || this.currentDate, timeZoneOffset)
      };

      console.log('fetchCampaignsMarketingData: Calling marketing finances API with payload:', marketingPayload);
      console.log('Campaign IDs being sent:', campaignIds);

      // Call marketing finances API
      this.commonService.getModuleListByAdvFilter(marketingPayload)
        .pipe(takeUntil(this.stopper))
        .subscribe({
          next: (response: any) => {
            console.log('Marketing Finances API Response:', response);
            if (response && response.data && response.data.length > 0) {
              this.campaignsMarketingData = response.data;
              console.log('Marketing data received:', this.campaignsMarketingData);
              // Update expanded account with marketing data if available
              this.updateExpandedAccountWithCampaignsMarketingData();
            } else {
              this.campaignsMarketingData = [];
              console.log('No marketing data found');
            }
          },
          error: (error) => {
            console.error('Error loading marketing finances data:', error);
            this.campaignsMarketingData = [];
          }
        });
    } catch (error) {
      console.error('fetchCampaignsMarketingData: Error preparing marketing data payload:', error);
    }
  }

  updateExpandedAccountWithCampaignsMarketingData(): void {
    if (!this.campaignsMarketingData || this.campaignsMarketingData?.length === 0) {
      console.log('updateExpandedAccountWithCampaignsMarketingData: No marketing data available');
      return;
    }
    const expandedAccount = this.filteredAccounts?.find(account => account?.isCampaignsExpanded);

    if (expandedAccount && expandedAccount?.campaigns) {
      console.log('updateExpandedAccountWithCampaignsMarketingData: Merging marketing data with campaigns');
      expandedAccount.campaigns = this.mergeCampaignsMarketingDataWithCampaigns(expandedAccount?.campaigns, this.campaignsMarketingData);
      expandedAccount.paginatedCampaigns = [...expandedAccount.campaigns];
      this.cdr.detectChanges();
    }
  }

  mergeCampaignsMarketingDataWithCampaigns(campaigns: any[], marketingData: any[]): any[] {
    if (!campaigns || !marketingData) {
      return campaigns || [];
    }

    return campaigns.map(campaign => {
      const marketingInfo = marketingData.find(marketing =>
        marketing.campaignId === campaign.campaignId ||
        marketing.campaignId === campaign.id ||
        marketing.id === campaign.campaignId ||
        marketing.id === campaign.id
      );

      if (marketingInfo) {
        return {
          ...campaign,
          ...marketingInfo,
          // Preserve original campaign data while adding marketing data
          campaignName: campaign.campaignName || marketingInfo.campaignName,
          campaignId: campaign.campaignId || campaign.id
        };
      }

      return campaign;
    });
  }

  initDeleteIntegration(accountId: string, accountName: string): void {
    if (!this.canDelete) {
      this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
      return;
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteAccount(accountId, accountName);
        }
      });
    }
  }

  getAppName(): string {
    return 'LeadRat';
  }

  openCustomGoogleCampaign(): void {
    const googleCampaignDetails = {
      image: 'assets/images/integration/google-ads.svg',
      displayName: 'Google Campaign',
      name: 'GoogleCampaign',
    };
    localStorage.setItem('integrationData', JSON.stringify(googleCampaignDetails));
    this.router.navigate(['/global-config/integration']);
  }

  updatePaginatedAds(account: any): void {
    const startIndex = (account.adsPageNumber - 1) * account.adsPageSize;
    const endIndex = startIndex + account.adsPageSize;
    account.paginatedAds = account.ads.slice(startIndex, endIndex);
    account.adsTotalCount = account.ads.length;
  }



  onAdsPageChange(event: any, account: any): void {
    account.adsPageNumber = event.offset + 1;
    this.updatePaginatedAds(account);
  }

  onCampaignsPageChange(event: any, account: any): void {
    account.campaignsPageNumber = event.offset + 1;
    account.isCampaignsLoading = true;
    this.loadCampaignsData(account);
  }

  assignAdsCount(account: any): void {
    account.adsPageNumber = 1;
    this.updatePaginatedAds(account);
  }

  assignCampaignsCount(account: any): void {
    account.campaignsPageNumber = 1;
    account.isCampaignsLoading = true;
    this.loadCampaignsData(account);
  }

  getPages(totalCount: number, pageSize: number): number {
    return Math.ceil(totalCount / pageSize);
  }

  isEmptyInput(): void {
    if (!this.searchTerm.trim()) {
      this.filteredAccounts = [...this.allAccounts];
    }
  }

  isEmptyCampaignsInput(): void {
    if (!this.campaignsSearchTerm.trim()) {
    }
  }

  onSearch(): void {
    const expandedAccount = this.filteredAccounts?.find(account => account?.isAdsExpanded);
    if (expandedAccount) {
      expandedAccount.isAdsLoading = true;
      this.loadAdsData(expandedAccount);
    }
  }

  onCampaignsSearch(): void {
    const expandedAccount = this.filteredAccounts?.find(account => account?.isCampaignsExpanded);
    if (expandedAccount) {
      expandedAccount.isCampaignsLoading = true;
      this.loadCampaignsData(expandedAccount);
    }
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
  }

  onFilterChanged(event: any): void {
  }

  onPickerOpened(date: Date): void {
  }

  onCplTrackChange(): void {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }

    const expandedAccount = this.filteredAccounts?.find(account => account?.isAdsExpanded);
    if (expandedAccount) {
      expandedAccount.isAdsLoading = true;
      this.loadAdsData(expandedAccount);
    }
    this.showCplFilter = false;
  }

  onCampaignsCplTrackChange(): void {
    if (!this.campaignsCplTrackForm.valid) {
      validateAllFormFields(this.campaignsCplTrackForm);
      return;
    }

    const expandedAccount = this.filteredAccounts?.find(account => account?.isCampaignsExpanded);
    if (expandedAccount) {
      expandedAccount.isCampaignsLoading = true;
      this.loadCampaignsData(expandedAccount);
    }
    this.showCampaignsCplFilter = false;
  }

  getStatusClasses(status: string): string {
    if (!status) return '';

    switch (status.toLowerCase()) {
      case 'active':
        return 'text-success fw-semi-bold';
      case 'paused':
        return 'text-warning fw-semi-bold';
      case 'inactive':
      case 'disabled':
        return 'text-danger fw-semi-bold';
      default:
        return 'text-muted fw-semi-bold';
    }
  }
}
